#!/usr/bin/env python3
"""
Gideon AI Avatar System - Flash TV Series Inspired
Professional animated face with state indicators
"""

import tkinter as tk
import customtkinter as ctk
import math
import time
import threading
from typing import Dict, Any, Optional, Callable
from PIL import Image, ImageDraw, ImageTk
import numpy as np

class GideonAvatar:
    """Flash TV series inspired Gideon AI avatar with professional animations"""
    
    def __init__(self, parent_frame, size: int = 200):
        self.parent = parent_frame
        self.size = size
        self.canvas_size = size + 40  # Extra space for glow effects
        
        # Animation state
        self.current_state = "idle"
        self.animation_running = False
        self.animation_thread = None
        
        # Animation parameters
        self.pulse_phase = 0
        self.wave_phase = 0
        self.particle_phase = 0
        
        # Colors (Flash-inspired blue/cyan theme)
        self.colors = {
            'primary': '#00D4FF',      # Bright cyan
            'secondary': '#0099CC',    # Darker cyan
            'accent': '#66E6FF',       # Light cyan
            'glow': '#00FFFF',         # Cyan glow
            'background': '#0A0E13',   # Dark background
            'inactive': '#334155',     # Inactive state
            'speaking': '#FF6B35',     # Orange for speaking
            'thinking': '#9333EA',     # Purple for thinking
            'listening': '#10B981'     # Green for listening
        }
        
        # Create avatar canvas
        self._create_avatar_canvas()
        
        # Initialize animation
        self._start_animation()
    
    def _create_avatar_canvas(self):
        """Create the avatar canvas with professional styling"""
        # Container frame
        self.avatar_container = ctk.CTkFrame(
            self.parent,
            width=self.canvas_size,
            height=self.canvas_size,
            fg_color="transparent"
        )
        self.avatar_container.pack(pady=20)
        self.avatar_container.pack_propagate(False)
        
        # Canvas for avatar
        self.canvas = tk.Canvas(
            self.avatar_container,
            width=self.canvas_size,
            height=self.canvas_size,
            bg=self.colors['background'],
            highlightthickness=0,
            relief='flat'
        )
        self.canvas.pack()
        
        # Center coordinates
        self.center_x = self.canvas_size // 2
        self.center_y = self.canvas_size // 2
        
        # Draw initial avatar
        self._draw_avatar()
    
    def _draw_avatar(self):
        """Draw the Gideon avatar with current state"""
        # Clear canvas
        self.canvas.delete("all")
        
        # Get state colors
        state_color = self._get_state_color()
        
        # Draw outer glow ring
        self._draw_glow_ring(state_color)
        
        # Draw main face circle
        self._draw_main_face(state_color)
        
        # Draw inner patterns
        self._draw_inner_patterns(state_color)
        
        # Draw state-specific elements
        self._draw_state_elements(state_color)
        
        # Draw center core
        self._draw_center_core(state_color)
    
    def _get_state_color(self) -> str:
        """Get color based on current state"""
        state_colors = {
            'idle': self.colors['primary'],
            'listening': self.colors['listening'],
            'thinking': self.colors['thinking'],
            'speaking': self.colors['speaking'],
            'processing': self.colors['accent']
        }
        return state_colors.get(self.current_state, self.colors['primary'])
    
    def _draw_glow_ring(self, color: str):
        """Draw outer glow ring with pulsing effect"""
        pulse_intensity = (math.sin(self.pulse_phase) + 1) / 2
        glow_radius = self.size // 2 + 15 + (pulse_intensity * 10)
        
        # Multiple glow layers for depth
        for i in range(5):
            alpha = int(50 - (i * 8))
            radius = glow_radius - (i * 3)
            
            self.canvas.create_oval(
                self.center_x - radius,
                self.center_y - radius,
                self.center_x + radius,
                self.center_y + radius,
                outline=color,
                width=2,
                stipple="gray25"
            )
    
    def _draw_main_face(self, color: str):
        """Draw main face circle"""
        radius = self.size // 2
        
        # Main circle
        self.canvas.create_oval(
            self.center_x - radius,
            self.center_y - radius,
            self.center_x + radius,
            self.center_y + radius,
            outline=color,
            width=3,
            fill=self.colors['background']
        )
        
        # Inner circle
        inner_radius = radius - 10
        self.canvas.create_oval(
            self.center_x - inner_radius,
            self.center_y - inner_radius,
            self.center_x + inner_radius,
            self.center_y + inner_radius,
            outline=color,
            width=2
        )
    
    def _draw_inner_patterns(self, color: str):
        """Draw inner geometric patterns"""
        # Rotating lines
        num_lines = 8
        line_length = 30
        
        for i in range(num_lines):
            angle = (i * 2 * math.pi / num_lines) + (self.wave_phase * 0.5)
            
            start_x = self.center_x + math.cos(angle) * 20
            start_y = self.center_y + math.sin(angle) * 20
            end_x = self.center_x + math.cos(angle) * (20 + line_length)
            end_y = self.center_y + math.sin(angle) * (20 + line_length)
            
            self.canvas.create_line(
                start_x, start_y, end_x, end_y,
                fill=color,
                width=2
            )
        
        # Concentric circles
        for radius in [40, 60, 80]:
            wave_offset = math.sin(self.wave_phase + radius * 0.1) * 5
            self.canvas.create_oval(
                self.center_x - radius - wave_offset,
                self.center_y - radius - wave_offset,
                self.center_x + radius + wave_offset,
                self.center_y + radius + wave_offset,
                outline=color,
                width=1,
                stipple="gray50"
            )
    
    def _draw_state_elements(self, color: str):
        """Draw state-specific visual elements"""
        if self.current_state == "listening":
            self._draw_listening_waves(color)
        elif self.current_state == "thinking":
            self._draw_thinking_particles(color)
        elif self.current_state == "speaking":
            self._draw_speaking_waves(color)
        elif self.current_state == "processing":
            self._draw_processing_spinner(color)
    
    def _draw_listening_waves(self, color: str):
        """Draw sound waves for listening state"""
        for i in range(3):
            wave_radius = 50 + (i * 20)
            wave_intensity = math.sin(self.wave_phase + i * 0.5)
            
            if wave_intensity > 0:
                self.canvas.create_oval(
                    self.center_x - wave_radius,
                    self.center_y - wave_radius,
                    self.center_x + wave_radius,
                    self.center_y + wave_radius,
                    outline=self.colors['listening'],
                    width=int(wave_intensity * 3) + 1
                )
    
    def _draw_thinking_particles(self, color: str):
        """Draw floating particles for thinking state"""
        num_particles = 12
        
        for i in range(num_particles):
            angle = (i * 2 * math.pi / num_particles) + self.particle_phase
            distance = 70 + math.sin(self.particle_phase + i) * 10
            
            x = self.center_x + math.cos(angle) * distance
            y = self.center_y + math.sin(angle) * distance
            
            size = 3 + math.sin(self.particle_phase + i * 0.5) * 2
            
            self.canvas.create_oval(
                x - size, y - size, x + size, y + size,
                fill=self.colors['thinking'],
                outline=""
            )
    
    def _draw_speaking_waves(self, color: str):
        """Draw audio waves for speaking state"""
        # Horizontal audio waves
        for i in range(5):
            y_offset = (i - 2) * 15
            wave_length = 60 + math.sin(self.wave_phase + i * 0.3) * 20
            
            self.canvas.create_line(
                self.center_x - wave_length,
                self.center_y + y_offset,
                self.center_x + wave_length,
                self.center_y + y_offset,
                fill=self.colors['speaking'],
                width=3
            )
    
    def _draw_processing_spinner(self, color: str):
        """Draw spinner for processing state"""
        num_segments = 8
        
        for i in range(num_segments):
            angle = (i * 2 * math.pi / num_segments) + self.wave_phase
            opacity = (math.sin(angle + self.wave_phase) + 1) / 2
            
            start_radius = 45
            end_radius = 65
            
            start_x = self.center_x + math.cos(angle) * start_radius
            start_y = self.center_y + math.sin(angle) * start_radius
            end_x = self.center_x + math.cos(angle) * end_radius
            end_y = self.center_y + math.sin(angle) * end_radius
            
            if opacity > 0.3:
                self.canvas.create_line(
                    start_x, start_y, end_x, end_y,
                    fill=color,
                    width=int(opacity * 4) + 1
                )
    
    def _draw_center_core(self, color: str):
        """Draw center core with pulsing effect"""
        pulse_size = 8 + math.sin(self.pulse_phase * 2) * 3
        
        self.canvas.create_oval(
            self.center_x - pulse_size,
            self.center_y - pulse_size,
            self.center_x + pulse_size,
            self.center_y + pulse_size,
            fill=color,
            outline=""
        )
        
        # Inner core
        inner_size = pulse_size * 0.6
        self.canvas.create_oval(
            self.center_x - inner_size,
            self.center_y - inner_size,
            self.center_x + inner_size,
            self.center_y + inner_size,
            fill=self.colors['background'],
            outline=""
        )
    
    def _start_animation(self):
        """Start the animation loop"""
        self.animation_running = True
        self.animation_thread = threading.Thread(target=self._animation_loop, daemon=True)
        self.animation_thread.start()
    
    def _animation_loop(self):
        """Main animation loop"""
        while self.animation_running:
            try:
                # Update animation phases
                self.pulse_phase += 0.1
                self.wave_phase += 0.15
                self.particle_phase += 0.08
                
                # Redraw avatar
                self.canvas.after(0, self._draw_avatar)
                
                # Control animation speed
                time.sleep(0.05)  # 20 FPS
                
            except Exception as e:
                print(f"Avatar animation error: {e}")
                break
    
    def set_state(self, state: str):
        """Set avatar state (idle, listening, thinking, speaking, processing)"""
        if state in ['idle', 'listening', 'thinking', 'speaking', 'processing']:
            self.current_state = state
    
    def stop_animation(self):
        """Stop the animation"""
        self.animation_running = False
        if self.animation_thread:
            self.animation_thread.join(timeout=1)
    
    def destroy(self):
        """Clean up avatar resources"""
        self.stop_animation()
        if hasattr(self, 'avatar_container'):
            self.avatar_container.destroy()


class GideonAvatarManager:
    """Manager for Gideon avatar integration"""
    
    def __init__(self, parent_frame):
        self.avatar = GideonAvatar(parent_frame)
        self.current_state = "idle"
    
    def set_listening(self):
        """Set avatar to listening state"""
        self.current_state = "listening"
        self.avatar.set_state("listening")
    
    def set_thinking(self):
        """Set avatar to thinking state"""
        self.current_state = "thinking"
        self.avatar.set_state("thinking")
    
    def set_speaking(self):
        """Set avatar to speaking state"""
        self.current_state = "speaking"
        self.avatar.set_state("speaking")
    
    def set_processing(self):
        """Set avatar to processing state"""
        self.current_state = "processing"
        self.avatar.set_state("processing")
    
    def set_idle(self):
        """Set avatar to idle state"""
        self.current_state = "idle"
        self.avatar.set_state("idle")
    
    def get_state(self) -> str:
        """Get current avatar state"""
        return self.current_state
    
    def destroy(self):
        """Clean up avatar manager"""
        if self.avatar:
            self.avatar.destroy()

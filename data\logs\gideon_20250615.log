2025-06-15 00:40:09,141 - SimpleInterface - ERROR - AI response error: 'NoneType' object has no attribute 'generate_response'
2025-06-15 00:41:07,433 - <PERSON><PERSON><PERSON> - INFO - Initializing Gideon Core System...
2025-06-15 00:41:07,437 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:41:07,438 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:41:07,438 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:41:07,439 - ModelManager - INFO - No existing models config found
2025-06-15 00:41:07,440 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:41:07,440 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:41:07,442 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:41:07,442 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:41:07,442 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:41:07,443 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:41:07,443 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:41:07,443 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:41:07,443 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:41:07,444 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:41:07,704 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:41:07,705 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:41:07,705 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:41:07,706 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:41:07,707 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:41:07,707 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:41:07,707 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:41:07,842 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (DualSense W', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Speakers (DualSense Wireless Co', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Speakers (DualSense Wireless Controller)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (DualSense Wireless Controller)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Speakers (DualSense Wireless Controller)', 'Headset Microphone (DualSense Wireless Controller)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:41:07,907 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (DualSense W
2025-06-15 00:41:08,047 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:41:08,047 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:41:11,066 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 9749.700694329327
2025-06-15 00:41:11,084 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:41:11,084 - STTEngine - INFO -    Energy threshold: 9749.700694329327
2025-06-15 00:41:11,085 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:41:11,085 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:41:11,085 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:41:11,085 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:41:11,085 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:41:11,085 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:41:11,086 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:41:11,348 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:41:11,348 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:41:11,348 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:41:14,627 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:41:20,649 - STTEngine - WARNING - Microphone test: No speech recognized
2025-06-15 00:41:24,853 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:41:24,854 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:41:24,855 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:41:24,855 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:41:42,320 - STTEngine - ERROR - Unexpected recognition error for ar-SA: [WinError 2] The system cannot find the file specified
2025-06-15 00:42:23,255 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'ما حدا فيكم حكى لي مرحبا' | Confidence: 0.87
2025-06-15 00:42:23,260 - STTEngine - INFO - 🎤 Heard: 'ما حدا فيكم حكى لي مرحبا'
2025-06-15 00:42:40,986 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'احنا في زمن مسخره' | Confidence: 0.79
2025-06-15 00:42:40,987 - STTEngine - INFO - 🎤 Heard: 'احنا في زمن مسخره'
2025-06-15 00:42:45,608 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'صبركم علي في المسخره' | Confidence: 0.81
2025-06-15 00:42:45,608 - STTEngine - INFO - 🎤 Heard: 'صبركم علي في المسخره'
2025-06-15 00:42:55,467 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:42:55,470 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:42:55,470 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:42:55,470 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:42:55,471 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:42:55,471 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:43:32,435 - STTEngine - INFO - 🌍 Detected language: ar | Text: 'شكو' | Confidence: 0.90
2025-06-15 00:43:32,435 - STTEngine - INFO - 🎤 Heard: 'شكو'
2025-06-15 00:44:14,113 - AIEngine - INFO - Generating response for: 'سلام'
2025-06-15 00:44:14,113 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:44:14,114 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:44:14,114 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:44:14,114 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:44:14,115 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:44:39,144 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:39,145 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:39,171 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,208 - AIEngine - INFO - LLM response generated successfully: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,208 - AIEngine - INFO - LLM generated successful response: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:44:55,212 - AIEngine - INFO - Final response generated: مرحباً، كيف يمكنني مساعدتك اليوم؟...
2025-06-15 00:51:57,926 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 00:51:57,931 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:51:57,931 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:51:57,931 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:51:57,932 - ModelManager - INFO - No existing models config found
2025-06-15 00:51:57,933 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:51:57,933 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:51:57,934 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:51:57,935 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:51:57,935 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:51:57,935 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:51:57,936 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:51:57,936 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:51:57,936 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:51:57,937 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:51:58,187 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:51:58,188 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:51:58,189 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:51:58,189 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:51:58,190 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:51:58,191 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:51:58,191 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:51:58,304 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:51:58,360 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 00:51:58,480 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:51:58,480 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:52:01,490 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 00:52:01,509 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:52:01,509 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 00:52:01,509 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:52:01,509 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:52:01,509 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:52:01,510 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:52:01,510 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:52:01,510 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:52:01,510 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:52:01,754 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:52:01,754 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:52:01,754 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:52:05,129 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:52:08,156 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 00:52:12,359 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:52:12,360 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:52:12,360 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:52:12,360 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:53:09,072 - AIEngine - INFO - Found Ollama models: ['qwen3:235b', 'gemma3:27b', 'deepseek-coder-v2:236b', 'dolphin-llama3:70b']
2025-06-15 00:53:19,514 - CleanProfessionalInterface - INFO - Model configuration updated: {'backend': 'ollama', 'model': 'qwen3:235b', 'always_llm': True, 'fallback_to_rules': True}
2025-06-15 00:53:37,541 - GideonMain - INFO - 🤖 Starting Gideon AI Assistant
2025-06-15 00:53:37,544 - GideonCore - INFO - Initializing Gideon Core System...
2025-06-15 00:53:37,551 - MemorySystem - INFO - Loaded 17 recent conversations
2025-06-15 00:53:37,551 - MemorySystem - INFO - Loaded 0 user preferences
2025-06-15 00:53:37,551 - MemorySystem - INFO - Memory system initialized successfully
2025-06-15 00:53:37,554 - ModelManager - INFO - No existing models config found
2025-06-15 00:53:37,554 - AIEngine - INFO - Initializing Hybrid AI Engine...
2025-06-15 00:53:37,555 - AIEngine - INFO - 🌍 AI Engine default response language set to: ar
2025-06-15 00:53:37,555 - AIEngine - INFO - Loaded 30 learned response patterns
2025-06-15 00:53:37,556 - AIEngine - INFO - Initializing LLM backends...
2025-06-15 00:53:37,556 - AIEngine - INFO - ✅ Ollama backend available
2025-06-15 00:53:37,556 - AIEngine - INFO - ℹ️ llama.cpp backend not available
2025-06-15 00:53:37,556 - AIEngine - INFO - ℹ️ CTransformers backend not available
2025-06-15 00:53:37,557 - AIEngine - INFO - ✅ Transformers backend available
2025-06-15 00:53:37,557 - AIEngine - INFO - Initialized 2 LLM backends
2025-06-15 00:53:37,557 - AIEngine - INFO - Loading model dolphin-llama3:70b via ollama...
2025-06-15 00:53:38,011 - LLM-Ollama - INFO - Ollama model loaded: dolphin-llama3:70b
2025-06-15 00:53:38,013 - AIEngine - INFO - ✅ Model loaded successfully: dolphin-llama3:70b
2025-06-15 00:53:38,013 - AIEngine - INFO - Loaded preferred model: dolphin-llama3:70b via ollama
2025-06-15 00:53:38,013 - AIEngine - INFO - 🚀 Initializing Ultra-Low Latency Mode...
2025-06-15 00:53:38,016 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:53:38,016 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:53:38,017 - AIEngine - INFO - ✅ AI model preloaded for ultra-low latency
2025-06-15 00:53:38,017 - AIEngine - INFO - ✅ Ultra-Low Latency Mode initialized
2025-06-15 00:53:38,017 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:53:38,017 - AIEngine - INFO - AI Engine initialized successfully
2025-06-15 00:53:38,017 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:53:38,018 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:53:38,018 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:53:38,018 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:53:38,018 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:53:38,019 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:53:38,019 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:53:38,019 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:53:38,019 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:53:39,027 - STTEngine - INFO - Available microphones: ['Microsoft Sound Mapper - Input', 'Headset Microphone (Razer Nari ', 'Microsoft Sound Mapper - Output', 'Headphones (Razer Nari Ultimate', 'Headset Earphone (Razer Nari Ul', 'Realtek Digital Output (Realtek', 'Speakers (Steam Streaming Speak', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Defin', 'Speakers (7.1 Surround Sound)', 'Primary Sound Capture Driver', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Primary Sound Driver', 'Headphones (Razer Nari Ultimate - Game)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Speakers (7.1 Surround Sound)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Realtek Digital Output (Realtek(R) Audio)', 'Speakers (Steam Streaming Speakers)', 'Speakers (THX Spatial)', 'LG ULTRAGEAR (NVIDIA High Definition Audio)', 'Headphones (Razer Nari Ultimate - Game)', 'Speakers (7.1 Surround Sound)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Stereo Mix (Realtek HD Audio Stereo input)', 'Speakers (Realtek HDA Primary output)', 'Headphones (Realtek HD Audio 2nd output)', 'Microphone (Realtek HD Audio Mic input)', 'SPDIF Out (Realtek HDA SPDIF Out)', 'Speakers 1 (THX Spatial Wave Speaker Headphone)', 'Speakers 2 (THX Spatial Wave Speaker Headphone)', 'Input (THX Spatial Wave Speaker Headphone)', 'Output (NVIDIA High Definition Audio)', 'Speakers 1 (7.1 Surround Sound Wave Speaker Headphone)', 'Speakers 2 (7.1 Surround Sound Wave Speaker Headphone)', 'Input (7.1 Surround Sound Wave Speaker Headphone)', 'Headset Microphone (Razer Nari Ultimate - Chat)', 'Headset Earphone (Razer Nari Ultimate - Chat)', 'Input (Steam Streaming Speakers Wave)', 'Speakers (Steam Streaming Speakers Wave)', 'Headphones (Razer Nari Ultimate - Game)']
2025-06-15 00:53:39,084 - STTEngine - INFO - 🎤 Selected preferred microphone: Headset Microphone (Razer Nari 
2025-06-15 00:53:39,213 - STTEngine - INFO - 🎤 Calibrating microphone for ambient noise...
2025-06-15 00:53:39,213 - STTEngine - INFO - Please be quiet for a moment while I calibrate...
2025-06-15 00:53:42,205 - STTEngine - INFO - ✅ Microphone calibrated. Energy threshold: 1.0214245934575312
2025-06-15 00:53:42,372 - STTEngine - INFO - 🔧 Recognizer configured:
2025-06-15 00:53:42,373 - STTEngine - INFO -    Energy threshold: 150
2025-06-15 00:53:42,373 - STTEngine - INFO -    Pause threshold: 0.8s
2025-06-15 00:53:42,373 - STTEngine - INFO -    Phrase threshold: 0.3s
2025-06-15 00:53:42,373 - STTEngine - INFO - 🎯 Wake word variants initialized: 10 English, 7 Arabic
2025-06-15 00:53:42,373 - STTEngine - INFO - 🌍 Auto language detection enabled (bilingual mode)
2025-06-15 00:53:42,373 - STTEngine - INFO - ✅ Enhanced speech recognition initialized successfully
2025-06-15 00:53:42,373 - STTEngine - INFO - 🌍 Bilingual mode: True
2025-06-15 00:53:42,373 - STTEngine - INFO - 🎯 Supported languages: ['en-US', 'ar-SA', 'ar-EG', 'ar-AE']
2025-06-15 00:53:44,577 - TTSEngine - INFO - 🎤 Using configured voice: Microsoft Zira Desktop - English (United States)
2025-06-15 00:53:44,577 - TTSEngine - INFO - Text-to-speech engine initialized successfully
2025-06-15 00:53:44,577 - GideonCore - INFO - 🎤 Testing microphone...
2025-06-15 00:53:48,945 - STTEngine - INFO - Testing microphone... Say something!
2025-06-15 00:53:51,986 - STTEngine - ERROR - Microphone test failed: listening timed out while waiting for phrase to start
2025-06-15 00:53:56,378 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:53:56,379 - GideonCore - INFO - Gideon Core System initialized successfully
2025-06-15 00:53:56,379 - STTEngine - INFO - Started continuous listening for wake word: 'gideon'
2025-06-15 00:53:56,379 - GideonCore - INFO - Always listening started with wake word: 'gideon'
2025-06-15 00:53:58,000 - TerminalManager - INFO - Created default terminal session: session_0
2025-06-15 00:53:59,086 - UltraProfessionalInterface - INFO - Terminal interface initialized successfully
2025-06-15 00:53:59,258 - UltraProfessionalInterface - INFO - ✅ Chat display widget created successfully
2025-06-15 00:53:59,258 - UltraProfessionalInterface - INFO - Chat widget type: CustomTkinter
2025-06-15 00:53:59,298 - UltraProfessionalInterface - INFO - ✅ Thread-safe animations started
2025-06-15 00:53:59,302 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Gideon AI Assistant Enterprise Edition - READY!...
2025-06-15 00:53:59,302 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Gideon A...
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-professional AI assistant with Flash-inspire...
2025-06-15 00:53:59,479 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Ultra-pr...
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Bilingual support active: Arabic (primary) / Engli...
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Bilingua...
2025-06-15 00:53:59,480 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Voice interaction ready - Say 'Gideon' + your ques...
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Voice in...
2025-06-15 00:53:59,481 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Ultra-low latency mode active for instant response...
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Ultra-lo...
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,482 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> AI Model loaded: dolphin-llama3:70b...
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: AI Model...
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,483 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Advanced AI capabilities ready - Multi-backend sup...
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Advanced...
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> Drag & drop new models to expand AI capabilities...
2025-06-15 00:53:59,484 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: Drag & d...
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🚀 Gideon AI Assistant Enterprise Edition initializ...
2025-06-15 00:53:59,485 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 🚀 Gideon...
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💼 Ultra-professional interface active with advance...
2025-06-15 00:53:59,493 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 💼 Ultra-...
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🎯 Real-time monitoring, performance analytics, and...
2025-06-15 00:53:59,498 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 🎯 Real-t...
2025-06-15 00:53:59,501 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,502 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,502 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 🧠 Enterprise AI Model: dolphin-llama3:70b...
2025-06-15 00:53:59,502 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 🧠 Enterp...
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💡 Advanced AI capabilities ready for complex enter...
2025-06-15 00:53:59,503 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 💡 Advanc...
2025-06-15 00:53:59,507 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,507 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:53:59,507 - UltraProfessionalInterface - INFO - 📝 Adding message: System -> 💬 Say 'Gideon' + your question, or type in the pro...
2025-06-15 00:53:59,508 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:53:59] ⚙️ System: 💬 Say 'G...
2025-06-15 00:53:59,508 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:53:59,508 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:54:01,521 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> Hello! I'm Gideon, your AI assistant. I'm ready to...
2025-06-15 00:54:01,521 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: [00:54:01] 🤖 Gideon: Hello! I'...
2025-06-15 00:54:01,523 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:54:01,523 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:54:18,612 - UltraProfessionalInterface - INFO - 📝 Adding message: You -> هلا...
2025-06-15 00:54:18,613 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ﻼﻫ :You 👤 [00:54:18]
...
2025-06-15 00:54:18,615 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ﻼﻫ...
2025-06-15 00:54:18,630 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:54:18,630 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT
2025-06-15 00:54:18,638 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:54:18,638 - AIEngine - INFO - Generating response for: 'هلا'
2025-06-15 00:54:18,638 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:54:18,638 - AIEngine - INFO - Attempting LLM response generation...
2025-06-15 00:54:18,638 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:54:18,638 - AIEngine - INFO - 🔍 Initial language detection: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🎯 Using explicitly set response language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - 🌍 Final prompt language: ar
2025-06-15 00:54:18,639 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:54:18,639 - AIEngine - INFO - Generating LLM response with max_tokens=150, temperature=0.9
2025-06-15 00:54:18,755 - UltraProfessionalInterface - INFO - 🎯 PRIMARY RESPONSE READY: 'أعمل على ذلك......'
2025-06-15 00:54:18,755 - UltraProfessionalInterface - INFO - 🔄 Handling text response on main thread: 'هلا' -> 'أعمل على ذلك......'
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - ✅ Displaying AI response: 'أعمل على ذلك......'
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - 🚀 DISPLAYING MESSAGE: 'أعمل على ذلك......'
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - 📝 Direct display for short response
2025-06-15 00:54:18,775 - UltraProfessionalInterface - INFO - 📝 Adding message: Gideon -> أعمل على ذلك......
2025-06-15 00:54:18,776 - UltraProfessionalInterface - INFO - 📝 Inserting CustomTkinter message: ...ﻚﻟﺫ ﻰﻠﻋ ﻞﻤﻋﺃ :Gideon 🤖 [00:...
2025-06-15 00:54:18,777 - UltraProfessionalInterface - INFO - 🔤 Arabic text processed: ...ﻚﻟﺫ ﻰﻠﻋ ﻞﻤﻋﺃ...
2025-06-15 00:54:18,801 - UltraProfessionalInterface - INFO - ✅ CustomTkinter message FORCE INSERTED successfully
2025-06-15 00:54:18,801 - UltraProfessionalInterface - INFO - ✅ MESSAGE VERIFIED IN WIDGET CONTENT

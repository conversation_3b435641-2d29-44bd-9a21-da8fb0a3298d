#!/usr/bin/env python3
"""
Gideon AI Assistant - Main Entry Point

A bilingual AI assistant with voice interaction capabilities.
Supports Arabic (primary) and English (secondary) languages.

Features:
- Bilingual speech recognition and text-to-speech
- Modern GUI interface
- AI model integration (Ollama, local models)
- Voice wake word detection
- Real-time language switching
"""

import sys
import os
import traceback
import threading
import time
from pathlib import Path

# Add src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Initialize application
print("Initializing Gideon AI Assistant...")
print("Bilingual support: Arabic (primary) / English (secondary)")

def check_dependencies():
    """Check for required dependencies"""
    print("Checking Dependencies...")
    print("=" * 40)
    
    missing_deps = []
    optional_deps = []
    
    # Check for CustomTkinter (required for modern UI)
    try:
        import customtkinter
        print("OK CustomTkinter available - Modern UI ready")
        modern_ui = True
    except ImportError:
        print("ERROR CustomTkinter REQUIRED for modern interface")
        print("Install with: pip install customtkinter")
        missing_deps.append('customtkinter')
        modern_ui = False
    
    # Check for core dependencies
    core_deps = [
        ('speech_recognition', 'speech_recognition'),
        ('pyttsx3', 'pyttsx3'),
        ('pyaudio', 'pyaudio'),
        ('Pillow', 'PIL'),
    ]
    
    for dep_name, import_name in core_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} available")
        except ImportError:
            missing_deps.append(dep_name)
            print(f"❌ {dep_name} missing")
    
    # Check for AI dependencies (optional but recommended)
    ai_deps = [
        ('ollama', 'ollama'),
        ('llama-cpp-python', 'llama_cpp'),
        ('transformers', 'transformers'),
        ('torch', 'torch'),
    ]
    
    ai_available = 0
    for dep_name, import_name in ai_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} available")
            ai_available += 1
        except ImportError:
            optional_deps.append(dep_name)
            print(f"⚠️ {dep_name} not available (optional)")
    
    # Check for performance monitoring dependencies
    perf_deps = [
        ('psutil', 'psutil'),
        ('numpy', 'numpy'),
    ]
    
    perf_available = 0
    for dep_name, import_name in perf_deps:
        try:
            __import__(import_name)
            print(f"OK {dep_name} available")
            perf_available += 1
        except ImportError:
            optional_deps.append(dep_name)
            print(f"WARNING {dep_name} not available (performance monitoring)")
    
    print(f"\nDependency Summary:")
    print(f"   AI Frameworks: {ai_available}/{len(ai_deps)} available")
    print(f"   Performance Tools: {perf_available}/{len(perf_deps)} available")
    
    if missing_deps:
        print(f"\nMissing REQUIRED dependencies: {', '.join(missing_deps)}")
        print("Install with: pip install " + " ".join(missing_deps))
        return False
    
    if optional_deps:
        print(f"\nOptional dependencies missing: {', '.join(optional_deps)}")
        print("For full features install: pip install " + " ".join(optional_deps))
    
    return True

def display_banner():
    """Display application banner"""
    banner = """
================================================================================
                                                                              
    GIDEON AI ASSISTANT                                                       
                                                                              
    Bilingual AI Assistant with Voice Interaction                            
                                                                              
    FEATURES:                                                                 
    • Bilingual Speech Recognition (Arabic/English)                          
    • Text-to-Speech with Female Voice                                       
    • Wake Word Detection ("Gideon")                                         
    • AI Model Integration (Ollama, Local Models)                            
    • Modern GUI Interface                                                   
    • Real-time Language Switching                                           
    • Chat History and Memory                                                
                                                                              
================================================================================
"""
    print(banner)

def main():
    """Main entry point for Gideon AI Assistant"""
    display_banner()

    # Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot start due to missing required dependencies")
        print("🔧 Please install the required packages and try again")
        input("Press Enter to exit...")
        return 1

    print("\n🚀 Starting Gideon AI Assistant...")
    print("🎨 Initializing modern interface...")

    try:
        # Import core components
        from src.core.gideon_core import GideonCore
        from src.ui.ultra_professional_interface import UltraProfessionalInterface
        from src.utils.logger import GideonLogger

        # Setup logging
        logger = GideonLogger("GideonMain")
        logger.info("🤖 Starting Gideon AI Assistant")

        # Create data directories
        print("📁 Creating data directories...")
        directories = [
            "data/models", "data/memory", "data/logs", "data/cache",
            "data/recordings", "data/screenshots"
        ]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)

        # Initialize Gideon core
        print("Initializing core systems...")
        gideon_core = GideonCore()

        # Initialize all systems
        print("Loading AI engine...")
        print("Configuring bilingual support...")
        gideon_core.initialize()

        # Create interface
        print("Creating user interface...")
        interface = UltraProfessionalInterface(gideon_core)
        root = interface.create_window()
        
        # Set window properties
        try:
            # Set window icon if available
            icon_path = current_dir / "assets" / "gideon_icon.ico"
            if icon_path.exists():
                root.iconbitmap(str(icon_path))
            
            # Set window attributes
            root.attributes('-topmost', False)  # Don't stay on top
            root.state('normal')  # Normal window state
            
        except Exception as e:
            logger.warning(f"Could not set window properties: {e}")
            logger.debug(traceback.format_exc())  # Log full traceback for debugging
        
        print("Gideon AI Assistant ready!")
        print("\nACTIVE FEATURES:")
        print("  • Modern dark theme interface")
        print("  • AI model integration")
        print("  • Bilingual support (Arabic primary/English secondary)")
        print("  • Voice interaction with wake word detection")
        print("  • Model management")
        print("  • Chat history and memory")

        print("\nINTERACTION METHODS:")
        print("  • Voice: Say 'Gideon' + your question")
        print("  • Chat: Type in the interface")
        print("  • Languages: Arabic (primary) / English (secondary)")
        print("  • Models: Manage AI models")

        print("\nKEYBOARD SHORTCUTS:")
        print("  • Enter: Send message")
        print("  • Ctrl+N: Clear chat history")
        print("  • Ctrl+L: Switch language")
        print("  • F1: Show help")
        
        # Add comprehensive welcome messages to interface
        interface._add_message("System", "Gideon AI Assistant Enterprise Edition - READY!", interface.design_system['colors']['accent_success'])
        interface._add_message("System", "Ultra-professional AI assistant with Flash-inspired interface", interface.design_system['colors']['text_secondary'])
        interface._add_message("System", "Bilingual support active: Arabic (primary) / English (secondary)", interface.design_system['colors']['accent_primary'])
        interface._add_message("System", "Voice interaction ready - Say 'Gideon' + your question", interface.design_system['colors']['accent_cyan'])
        interface._add_message("System", "Ultra-low latency mode active for instant responses", interface.design_system['colors']['accent_purple'])

        # Check AI status and display comprehensive information
        if gideon_core and hasattr(gideon_core, 'ai_engine'):
            if gideon_core.ai_engine.llm_enabled:
                model_name = gideon_core.ai_engine.active_backend.current_model if gideon_core.ai_engine.active_backend else "Unknown"
                interface._add_message("System", f"AI Model loaded: {model_name}", interface.design_system['colors']['accent_success'])
                interface._add_message("System", "Advanced AI capabilities ready - Multi-backend support active", interface.design_system['colors']['text_secondary'])
                interface._add_message("System", "Drag & drop new models to expand AI capabilities", interface.design_system['colors']['accent_warning'])
            else:
                interface._add_message("System", "Using rule-based responses. Configure AI models for advanced capabilities.", interface.design_system['colors']['accent_warning'])
                interface._add_message("System", "Tip: Install Ollama or add model files for enhanced AI features", interface.design_system['colors']['text_secondary'])
        
        # Run the ultra-professional interface
        interface.run()
        
        logger.info("Gideon AI Assistant Enterprise Edition shutting down")
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
        return 0
        
    except Exception as e:
        # Log fatal error and traceback for diagnostics
        try:
            from src.utils.logger import GideonLogger
            logger = GideonLogger("GideonMain")
            logger.error(f"Fatal error: {e}")
            logger.error(traceback.format_exc())
        except Exception:
            pass  # If logger import fails, fallback to print
        print(f"\n❌ Fatal error: {e}")
        print("\n🔍 Full error details:")
        traceback.print_exc()
        
        # Try to show error in GUI
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()  # Hide main window
            
            messagebox.showerror(
                "Gideon AI Enterprise - Fatal Error",
                f"Failed to start Gideon AI Assistant Enterprise Edition:\n\n{e}\n\nCheck console for details."
            )
            
        except Exception:
            pass
        
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    sys.exit(main())

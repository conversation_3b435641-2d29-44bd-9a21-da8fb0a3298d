# 🤖 Gideon AI Assistant - Ultra Professional Enterprise Edition

The ultimate professional AI assistant with Flash-inspired interface, advanced AI capabilities, and enterprise-grade features. Featuring ultra-low latency performance, bilingual support, and comprehensive model management.

## ✨ Ultra-Professional Features

- **🎨 Flash-Inspired Interface**: Ultra-professional dark theme with enterprise-grade design
- **🧠 Advanced AI Engine**: dolphin-llama3:70b model with multi-backend support (Ollama + Transformers + CTransformers)
- **🌍 Bilingual Excellence**: Arabic primary language with English secondary support
- **🎤 Voice Interaction**: Wake word detection ("Gideon") with continuous listening
- **⚡ Ultra-Low Latency**: Optimized performance for instant responses
- **🔄 Model Management**: Drag & drop functionality for adding new AI models
- **📊 Performance Monitoring**: Real-time system metrics and AI status dashboard
- **💼 Enterprise Design**: Professional animations, effects, and navigation
- **🎯 Advanced Features**: Memory system, learning capabilities, and contextual responses
- **🔧 Professional Tools**: Comprehensive keyboard shortcuts and advanced controls

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Windows, macOS, or Linux
- Microphone for voice input (recommended)
- Speakers/headphones for voice output (recommended)
- 4GB+ RAM for optimal AI performance

### Installation

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd gideon-ai
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run Gideon Ultra-Professional Edition**:
   ```bash
   python main_ultra_pro.py
   ```

That's it! Gideon will start with the ultra-professional Flash-inspired interface featuring enterprise-grade design and advanced AI capabilities.

## 📋 Dependencies

The application will automatically check for missing dependencies and guide you through installation.

### Core Dependencies (Required)
- `customtkinter` - Ultra-professional GUI framework
- `SpeechRecognition` - Advanced speech-to-text functionality
- `pyttsx3` - Text-to-speech engine with voice selection
- `Pillow` - Image processing and graphics
- `pyaudio` - Audio input/output handling

### AI Dependencies (Recommended)
- `ollama` - Local LLM platform (for dolphin-llama3:70b)
- `transformers` - Hugging Face transformers library
- `torch` - PyTorch neural network framework
- `llama-cpp-python` - GGUF model support
- `ctransformers` - Alternative local LLM support

### Performance Dependencies (Optional)
- `psutil` - System performance monitoring
- `numpy` - Numerical computing optimization

## 🎮 Usage

### Professional Chat Interface
- Type messages in the ultra-professional chat interface
- Press Enter to send messages
- Enjoy real-time AI responses with ultra-low latency
- View conversation history with professional formatting

### Voice Interaction
- **Wake Word**: Say "Gideon" followed by your question
- **Continuous Listening**: Always-on wake word detection
- **Bilingual Support**: Speak in Arabic or English
- **Female Voice**: Professional female voice responses

### Model Management
- **Drag & Drop**: Add new AI models by dragging files to the interface
- **Real-time Switching**: Switch between available models instantly
- **Performance Monitoring**: Monitor model performance and status
- **Multi-backend Support**: Ollama, Transformers, and CTransformers

### Professional Features
- **Performance Dashboard**: Real-time system metrics
- **AI Status Monitor**: Live AI model status and performance
- **Navigation Sidebar**: Professional navigation with scroll support
- **Keyboard Shortcuts**: Advanced shortcuts for power users
- **Enterprise Design**: Flash-inspired professional interface

### Bilingual Excellence
- **Arabic Primary**: Default language with right-to-left text support
- **English Secondary**: Seamless language switching
- **Voice Commands**: Work in both Arabic and English
- **Professional Localization**: Enterprise-grade language support

## 🔧 Configuration

Gideon creates a configuration file at `data/config.json` with customizable settings:

```json
{
    "app": {
        "language": "en",
        "theme": "dark"
    },
    "speech": {
        "stt_enabled": true,
        "tts_enabled": true,
        "tts_rate": 200,
        "tts_volume": 0.8
    },
    "ui": {
        "window_width": 1200,
        "window_height": 800
    }
}
```

## 📁 Project Structure

```
gideon-ai/
├── main_ultra_pro.py      # PRIMARY ENTRY POINT - Ultra Professional Edition
├── requirements.txt       # Dependencies
├── README.md             # This file
├── src/
│   ├── core/             # Core AI system
│   │   ├── gideon_core.py # Main orchestrator
│   │   ├── ai_engine.py   # Hybrid AI processing (Ollama + Transformers + CTransformers)
│   │   ├── memory_system.py # Memory and learning
│   │   └── model_manager.py # AI model management
│   ├── speech/           # Speech processing
│   │   ├── stt_engine.py  # Advanced speech-to-text
│   │   └── tts_engine.py  # Text-to-speech with voice selection
│   ├── ui/               # User interface
│   │   ├── ultra_professional_interface.py # Ultra-professional GUI
│   │   ├── gideon_avatar.py # Flash-inspired avatar
│   │   └── voice_chat_interface.py # Voice chat interface
│   ├── optimization/     # Performance optimization
│   │   └── performance_optimizer.py # Ultra-low latency optimization
│   ├── system/           # System integration
│   │   ├── screen_capture.py # Screenshot functionality
│   │   └── voice_commands.py # Voice command processing
│   └── utils/            # Utilities
│       ├── config.py      # Configuration management
│       ├── logger.py      # Logging system
│       ├── i18n.py        # Internationalization
│       ├── gender_consistency.py # Female identity consistency
│       └── text_direction.py # RTL/LTR text handling
└── data/                 # Data storage
    ├── config.json       # Configuration file
    ├── logs/             # Log files
    ├── memory/           # Memory database
    ├── models/           # AI model storage
    ├── cache/            # Temporary files
    └── recordings/       # Voice recordings
```

## 🎯 Voice Commands

### English Commands
- "screenshot" / "take screenshot" - Capture screen
- "time" / "what time" - Get current time
- "date" / "what date" - Get current date
- "help" / "what can you do" - Show help
- "stop listening" - Disable voice input
- "start listening" - Enable voice input
- "mute" - Mute voice output
- "unmute" - Unmute voice output

### Arabic Commands (الأوامر العربية)
- "لقطة شاشة" - التقاط الشاشة
- "الوقت" / "كم الساعة" - معرفة الوقت
- "التاريخ" - معرفة التاريخ
- "مساعدة" - إظهار المساعدة
- "توقف عن الاستماع" - إيقاف الاستماع

## 🧠 AI Capabilities

Gideon uses a local AI engine that:
- Learns from conversations
- Remembers user preferences
- Provides contextual responses
- Supports pattern matching
- Handles multiple languages
- Improves over time

## 🔍 Troubleshooting

### Common Issues

1. **Speech recognition not working**:
   - Check microphone permissions
   - Ensure microphone is not muted
   - Try running: `python -c "import speech_recognition; print('OK')"`

2. **Text-to-speech not working**:
   - Check speaker/headphone connection
   - Verify system audio settings
   - Try running: `python -c "import pyttsx3; print('OK')"`

3. **GUI not appearing**:
   - Install CustomTkinter: `pip install customtkinter`
   - Check display settings
   - Try running in compatibility mode

4. **Dependencies missing**:
   - Run: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

### Logs

Check log files in `data/logs/` for detailed error information.

## 🔒 Privacy & Security

- **No external APIs**: All processing is done locally
- **No data transmission**: Your conversations stay on your device
- **Local storage**: All data stored in local `data/` directory
- **Optional features**: Speech and system integration can be disabled

## 🛠️ Development

### Adding Custom Commands

```python
# In voice_commands.py
def my_custom_command(self, text: str) -> str:
    return "Custom response"

# Register the command
self.register_command(
    ["my command", "custom"],
    my_custom_command,
    "Description of my command"
)
```

### Extending AI Responses

```python
# In ai_engine.py
# Add to knowledge_base
self.knowledge_base["new_topic"] = [
    "Response 1",
    "Response 2"
]
```

## 📄 License

This project is open source. Feel free to modify and distribute.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues.

## 📞 Support

If you encounter any issues:
1. Check the troubleshooting section
2. Review log files in `data/logs/`
3. Open an issue with detailed error information

---

**Enjoy using Gideon AI Assistant! 🤖✨**
